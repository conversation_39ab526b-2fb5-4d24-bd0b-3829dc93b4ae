<template>
  <basic-container>
    <avue-crud :option="option"
               v-model:search="search"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               :upload-before="uploadBefore"
               :upload-after="uploadAfter"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.photoAlbum_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="warning"
                   plain
                   icon="el-icon-download"
                   @click="handleExport">导 出
        </el-button>
      </template>
      
      <template #menu="{ row }">
        <el-button type="primary"
                   icon="el-icon-download"
                   text
                   @click="handleDownloadImage(row)">下载图片
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/photoalbum/photoAlbum";
  import option from "@/option/photoalbum/photoAlbum";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/utils/auth';
  import {downloadXls} from "@/utils/util";
  import {dateNow} from "@/utils/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.photoAlbum_add, false),
          viewBtn: this.validData(this.permission.photoAlbum_view, false),
          delBtn: this.validData(this.permission.photoAlbum_delete, false),
          editBtn: this.validData(this.permission.photoAlbum_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        let downloadUrl = `/hy/photoAlbum/export-photoAlbum?${this.website.tokenHeader}=${getToken()}`;
        const {
            title,
            category,
        } = this.query;
        let values = {
            title_like: title,
            category_equal: category,
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `会议相册表${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      /**
       * 下载图片
       */
      handleDownloadImage(row) {
        if (!row.imageUrl) {
          this.$message.warning('该照片暂无图片链接，无法下载');
          return;
        }

        try {
          // 获取文件扩展名
          const url = row.imageUrl;
          const urlParts = url.split('.');
          const extension = urlParts.length > 1 ? urlParts[urlParts.length - 1] : 'jpg';

          // 生成文件名
          const fileName = `${row.title || '照片'}_${row.id}.${extension}`;

          // 使用fetch获取图片数据，实现直接下载
          fetch(row.imageUrl)
            .then(response => {
              if (!response.ok) {
                throw new Error('网络请求失败');
              }
              return response.blob();
            })
            .then(blob => {
              // 创建blob URL
              const url = window.URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.download = fileName;
              link.style.display = 'none';

              // 触发下载
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // 清理blob URL
              window.URL.revokeObjectURL(url);

              this.$message.success(`下载完成：${row.title || '照片'}`);
            })
            .catch(error => {
              console.error('下载失败:', error);
              this.$message.error('下载失败，请稍后重试');
            });
        } catch (error) {
          console.error('下载图片失败:', error);
          this.$message.error('下载失败，请稍后重试');
        }
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      /**
       * 图片上传成功后的处理
       */
      uploadAfter(res, done, loading, column) {
        console.log('上传响应数据:', res); // 调试日志

        if (res && res.link) {
          // 只设置imageUrl字段，不干扰imageFile的回显
          this.form.imageUrl = res.link;
          this.$message.success('图片上传成功！');
        }

        // 无论成功失败都调用done，让avue-crud处理回显
        done();
      },

      onLoad(page, params = {}) {
        this.loading = true;

        const {
          title,
          category,
        } = this.query;

        let values = {
          title_like: title,
          category_equal: category,
        };

        getList(page.currentPage, page.pageSize, values).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
